<?php

use models\composite\oDrawManagement\DrawRequestManager;
use models\composite\oDrawManagement\BorrowerDrawRequest;

global $LMRId;

try {
    $drawRequestManager = DrawRequestManager::forLoanFile($LMRId);
    $requestData = $drawRequestManager->getDrawRequestDataArray();
    $categoriesData = [];

    if(isset($requestData['status']) && $requestData['status'] !== BorrowerDrawRequest::STATUS_NEW ) {
        $categoriesData = $requestData['categories'];
    }
} catch (Exception $e) {
    echo "Error loading draw request data: " . htmlspecialchars($e->getMessage()) . " -->";
    return;
}

$displayStatus = '';
$displayStatusClass = '';
$disableAction = false;
if($requestData && $requestData['status'] && !$requestData['isDrawRequest']) {
    switch($requestData['status']) {
        case BorrowerDrawRequest::STATUS_NEW:
            $disableAction = true;
            break;
        case BorrowerDrawRequest::STATUS_PENDING:
            $displayStatus = 'Submitted';
            $displayStatusClass = 'bg-warning';
            break;
        case BorrowerDrawRequest::STATUS_APPROVED:
            $displayStatus = 'Draw 1 Pending';
            $displayStatusClass = 'bg-success';
            $disableAction = true;
            break;
        case BorrowerDrawRequest::STATUS_REJECTED:
            $displayStatus = 'Rejected';
            $displayStatusClass = 'bg-danger';
            break;
    }
}

?>
<style>
.work-table {
    background: #fff;
    border-radius: 5px;
    overflow: hidden;
}

.work-table thead {
    background: #f3f6f9;
}

.work-table th {
    font-size: 1rem !important;
    color: #495057;
    border: none;
    text-transform: capitalize;
}

.work-table td {
    padding: 1rem 0.75rem;
    border: none;
}

.work-table tbody tr:hover {
    background-color: #f8f9ff;
    transition: all 0.2s ease;
}

.category-header {
    background: #e1f0ff;
    font-weight: 600;
    font-size: 1rem;
    text-transform: uppercase;
    padding: 0.75rem 1rem;
}

.line-item {
    border-bottom: 1px solid #ebedf3 !important;
}

.line-item td:first-child {
    font-weight: 600;
}

.percentage {
    font-weight: 600;
    font-size: 0.9rem;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    color: #fff;
    display: inline-block;
    width: 45px;
    text-align: center;
    text-shadow: 0px 0px 2px rgba(0, 0, 0, 0.5);
}

.tooltipClass {
    font-size: 1rem;
}

.note-btn {
    display: contents;
    padding: 5px 7px !important;
    margin-left: calc(0.5rem - 0.25rem);
}

.note-btn i {
    padding: 0 !important;
}

.lender-note-btn {
    display: contents;
    padding: 0 !important;
    margin-left: calc(0.5rem - 0.25rem);
}

.lender-note-btn i {
    padding: 0 !important;
}

.lender-notes-modal .modal-dialog {
    max-width: 500px;
}

.lender-notes-modal .modal-body {
    padding: 20px;
}

.lender-notes-textarea {
    width: 100%;
    min-height: 120px;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 12px;
    font-size: 14px;
    resize: vertical;
    font-family: inherit;
}

.lender-notes-textarea:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    outline: 0;
}

.lender-notes-modal .modal-footer {
    border-top: 1px solid #dee2e6;
    padding: 15px 20px;
}

.lender-notes-modal .modal-header {
    border-bottom: 1px solid #dee2e6;
    padding: 15px 20px;
}

.lender-notes-modal .modal-title {
    font-size: 1.1rem;
    font-weight: 600;
}
span.badge {
    font-size: 1rem;
}
.col-reject-reason {
    padding: 0 !important;
}
</style>
<div class="card card-body p-0">
    <div class="card card-custom card-stretch d-flex p-0 drawManagementCard">
        <div class="card-header card-header-tabs-line bg-gray-100">
            <div class="card-title">
                <h3 class="card-label">
                    Draw Management
                </h3>
            </div>
            <div class="card-toolbar ">
                <a href="javascript:void(0);"
                    class="tooltipClass btn  btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass"
                    data-card-tool="toggle" data-section="drawManagementCard" data-toggle="tooltip" data-placement="top"
                    title="" data-original-title="Toggle Card">
                    <i class="ki ki-arrow-down icon-nm"></i>
                </a>
                <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary mr-1 d-none" data-card-tool="reload"
                    data-toggle="tooltip" data-placement="top" title="" data-original-title="Reload Card">
                    <i class="ki ki-reload icon-nm"></i>
                </a>
                <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary d-none" data-card-tool="remove"
                    data-toggle="tooltip" data-placement="top" title="" data-original-title="Remove Card">
                    <i class="ki ki-close icon-nm"></i>
                </a>
            </div>
        </div>

        <div class="card-body p-2">
            <div class="row">
                <div class="col-12">
                    <div class="row">
                        <div class="d-flex justify-content-start mb-4 mt-2 col-md-6">
                            <?php if($requestData) { ?>
                                <span class="badge d-inline-flex align-items-center <?= $displayStatusClass; ?>">
                                    <i class="fas fa-info-circle mr-2 text-dark"></i>
                                    <?= $displayStatus; ?></span>
                            <?php } ?>
                        </div>
                        <div class="d-flex justify-content-end mb-4 mt-2 col-md-6">
                            <?php if(!$disableAction): ?>
                            <div class="col-md-4 d-flex align-items-end">
                                <label class="mr-2 font-weight-bold" for="status">Action:</label>
                                <select class="form-control input-sm statusAction" name="status" id="status">
                                    <option <?= ($requestData['status'] ?? '') !== BorrowerDrawRequest::STATUS_REJECTED ? 'selected' : ''; ?> value="<?= BorrowerDrawRequest::STATUS_APPROVED; ?>">Accept</option>
                                    <option <?= ($requestData['status'] ?? '') === BorrowerDrawRequest::STATUS_REJECTED ? 'selected' : ''; ?> value="<?= BorrowerDrawRequest::STATUS_REJECTED; ?>">Reject</option>
                                </select>
                            </div>
                            <?php endif; ?>
                        <div class="d-flex align-items-end">
                            <button class="btn btn-primary btn-sm" type="button">
                                <i class="fas fa-download mr-2"></i>Export Table
                            </button>
                        </div>
                    </div>
                    </div>
                    <div class="work-table">
                        <?php
                        if($requestData && isset($requestData['sowApproved']) && $requestData['sowApproved']):
                            require 'partials/_table-draw-request.php';
                        else:
                            require 'partials/_table-scope-of-work.php';
                        endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Action Buttons -->
<div class="d-flex justify-content-center btn-sm action-buttons">
    <button type="submit" name="btnSave" id="btnSave" class="btn btn-primary" <?= $disableAction ? 'disabled' : ''; ?>>Save</button>
</div>

<!-- Lender Notes Modal -->
<div class="modal fade lender-notes-modal" id="lenderNotesModal" tabindex="-1" role="dialog" aria-labelledby="lenderNotesModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="lenderNotesModalLabel">Lender Notes</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <textarea class="form-control lender-notes-textarea" id="lenderNotesTextarea" placeholder="Enter lender notes for this line item..."></textarea>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveLenderNote">Save Notes</button>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    $('.percentage').each(function() {
        const percentage = parseInt($(this).text());
        $(this).css('background', getPercentageColor(percentage));
    });

    function handleStatusChange() {
        const selectedValue = $('.statusAction').val();
        if (selectedValue === '<?= BorrowerDrawRequest::STATUS_REJECTED; ?>') {
            $('.col-reject-reason').removeClass('hide');
        } else {
            $('.col-reject-reason').addClass('hide');
        }
    }

    $('.statusAction').on('change', handleStatusChange);
    handleStatusChange();

    initializeLenderNotesModal();

    <?php if(!$disableAction): ?>
    $('#btnSave').on('click', function(e) {
        e.preventDefault();
        submitDrawManagementData();
    });
    <?php endif; ?>
});

function getPercentageColor(percentage) {
    // Ensure percentage is between 0 and 100
    const p = Math.max(0, Math.min(100, percentage));
    const saturation = 100;
    const lightness = 45;
    const hue = (p / 100) * 120;

    return `hsl(${hue}, ${saturation}%, ${lightness}%)`;
}

function initializeLenderNotesModal() {
    const $buttons = $('.lender-note-btn');
    if ($buttons.length === 0) {
        console.warn('No lender note buttons found. Check if the table is loaded and buttons have the correct class.');
        return;
    }

    let currentLineItemId = null;
    let currentButton = null;

    $(document).on('click', '.lender-note-btn', function(e) {
        e.preventDefault();
        e.stopPropagation();

        const $btn = $(this);
        currentLineItemId = $btn.data('line-item-id');
        currentButton = $btn;
        const currentNote = $btn.closest('tr').find('input').val() || '';

        $('#lenderNotesTextarea').val(currentNote);
        $('#lenderNotesModal').modal('show');
    });

    $('#saveLenderNote').on('click', function() {
        const newNote = $('#lenderNotesTextarea').val().trim();

        if (currentButton) {
            const $icon = currentButton.find('i');
            $icon.attr('data-original-title', newNote);
            currentButton.closest('tr').find('input').val(newNote);

            if (newNote) {
                $icon.removeClass('text-muted').addClass('text-primary');
            } else {
                $icon.removeClass('text-primary').addClass('text-muted');
            }
        }

        $('#lenderNotesModal').modal('hide');
    });

    $('#lenderNotesModal').on('hidden.bs.modal', function() {
        currentLineItemId = null;
        currentButton = null;
        $('#lenderNotesTextarea').val('');
    });
}

function submitDrawManagementData() {
    const status = $('#status').val();
    const lmrid = <?= $LMRId; ?>;
    const lineItems = {};

    $('.line-item').each(function() {
        const $row = $(this);
        const lineItemId = $row.find('.lender-note-btn').data('line-item-id');

        if (lineItemId) {
            const lenderNotes = $row.find('input[name="lenderNotes"]').val() || '';
            const rejectReason = status === 'rejected' ? $row.find('select[name="rejectReason"]').val() : '';

            lineItems[lineItemId] = {
                lenderNotes: lenderNotes,
                rejectReason: rejectReason
            };
        }
    });

    const postData = {
        lmrid: lmrid,
        status: status,
        lineItems: lineItems
    };

    $.ajax({
        url: '/backoffice/api_v2/draw_management/LoanFile',
        type: 'POST',
        contentType: 'application/json',
        dataType: 'json',
        data: JSON.stringify(postData),
        beforeSend: function() {
            $('#btnSave').prop('disabled', true).text('Saving...');
        },
        success: function(response) {
            if (response.success) {
                toastrNotification('Draw Request Status Updated!', 'success');
                setTimeout(function() {
                    location.reload();
                }, 1500);
            } else {
                toastrNotification('Error: ' + (response.message || 'Failed to update Draw Request Status'), 'error');
            }
        },
        error: function(xhr, status, error) {
            console.error('AJAX Error:', xhr, status, error);
            let errorMsg = xhr.responseJSON && xhr.responseJSON.message ? xhr.responseJSON.message : 'An error occurred while saving data. Please try again.';
            toastrNotification(errorMsg, 'error');
        },
        complete: function() {
            $('#btnSave').prop('disabled', false).text('Save');
        }
    });
}

</script>
