<?php
use models\composite\oDrawManagement\BorrowerDrawRequest;

$borrowerDrawRequest = null;
$categoriesData = [];

if($drawRequest && $drawRequest->id) {
    $borrowerDrawRequest = new BorrowerDrawRequest($drawRequest);
    $drawRequestData = $borrowerDrawRequest->toArray();
    $categoriesData = $drawRequestData['categories'] ?? [];
}
?>
<style>
.card {
    padding: 0 !important;
}

/* Notes button styling */
.popup-container {
    position: relative;
    display: inline-block;
}

.note-btn {
    border: none;
    background: transparent;
    cursor: pointer;
    transition: all 0.2s ease;
}

.note-btn:hover {
    transform: scale(1.1);
}

.note-btn:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

/* Show popover on hover */
.popup-container:hover .popover {
    display: block;
}

/* Hide popover if empty */
.popup-container .popover:empty {
    display: none;
}

/* Validation styling */
.is-invalid {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.validation-message {
    font-size: 0.875rem;
    margin-top: 0.25rem;
}
</style>

<div class="card-body">
    <?php if(isset($displayStatus)) { ?>
        <div class="alert <?= $displayStatusClass; ?>" role="alert">
            <i class="fas fa-info-circle"></i>
            <?= $displayStatus; ?>
        </div>
    <?php } ?>
    <div class="d-flex justify-content-between align-items-center mb-3">
        <div>
            <h4 class="mb-0 text-success">Manage Draw Request</h4>
        </div>
    </div>

    <!-- Draw Request Table -->
    <?php if (!empty($categoriesData)): ?>
        <table class="table line-item-table table-bordered">
            <thead class="thead-light">
                <colgroup>
                    <col style="width:25%">
                    <col style="width:15%">
                    <col style="width:15%">
                    <col style="width:15%">
                    <col style="width:10%">
                    <col style="width:30%">
                    <col style="width:6%">
                </colgroup>
                <tr>
                    <th scope="col">Line Item</th>
                    <th scope="col">Total Budget</th>
                    <th scope="col">Completed Renovations</th>
                    <th scope="col">Prv. Disbursed Amount</th>
                    <th scope="col">% Completed</th>
                    <th scope="col">Requested Amount (% | $)</th>
                    <th scope="col">Notes</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($categoriesData as $category): ?>
                    <?php if (!empty($category['lineItems'])): ?>
                        <!-- Category Header -->
                        <tr class="category-header">
                            <td colspan="7" class="font-weight-bold bg-light">
                                <?= htmlspecialchars(strtoupper($category['name'])) ?>
                                <?php if (!empty($category['description'])): ?>
                                    <div class="popup-container">
                                        <i class="fa fa-info-circle text-primary ml-2"><span class="text"></span></i>
                                        <div class="popover"><?= htmlspecialchars($category['description']) ?></div>
                                    </div>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <!-- Line Items -->
                        <?php foreach ($category['lineItems'] as $lineItem): ?>
                            <tr class="line-item">
                                <td>
                                    <?= htmlspecialchars($lineItem['name']) ?>
                                    <?php if (!empty($lineItem['description'])): ?>
                                        <div class="popup-container">
                                            <i class="fa fa-info-circle text-primary ml-2"><span class="text"></span></i>
                                            <div class="popover"><?= htmlspecialchars($lineItem['description']) ?></div>
                                        </div>
                                    <?php endif; ?>
                                </td>
                                <td>$<?= number_format($lineItem['cost'], 2) ?></td>
                                <td>$<?= number_format($lineItem['completedAmount'], 2) ?></td>
                                <td>$0.00</td>
                                <td><?= round($lineItem['completedPercent']) ?>%</td>
                                <td>
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text bold">%</span>
                                        </div>
                                        <input type="number"
                                                name="requestedPercent"
                                                class="form-control requested-percent"
                                                min="0"
                                                step="0.01"
                                                value="<?= round($lineItem['requestedAmount'] / $lineItem['cost'] * 100) ?>"
                                                data-line-item-id="<?= $lineItem['id'] ?>"
                                                data-cost="<?= $lineItem['cost'] ?>"
                                                data-completed-amount="<?= $lineItem['completedAmount'] ?>"
                                                data-completed-percent="<?= round($lineItem['completedPercent'], 2) ?>"
                                                style="max-width: 70px;"/>
                                        <div class="input-group-prepend">
                                            <span class="input-group-text bold">$</span>
                                        </div>
                                        <input type="number"
                                                name="requestedAmount"
                                                class="form-control requested-amount"
                                                min="0"
                                                step="0.01"
                                                value="<?= $lineItem['requestedAmount'] ?>"
                                                data-line-item-id="<?= $lineItem['id'] ?>"
                                                data-cost="<?= $lineItem['cost'] ?>"
                                                data-completed-amount="<?= $lineItem['completedAmount'] ?>"
                                                data-completed-percent="<?= round($lineItem['completedPercent'], 2) ?>"/>
                                    </div>
                                    <div class="validation-message text-danger small mt-1" style="display: none;"></div>
                                </td>
                                <td class="text-center">
                                    <div class="popup-container">
                                        <button type="button" class="btn p-0 note-btn"
                                            data-toggle="modal"
                                            data-target="#noteModal"
                                            data-note="<?= htmlspecialchars($lineItem['notes']) ?>"
                                            data-line-item-id="<?= $lineItem['id'] ?>"
                                            title="Notes">
                                            <i class="icon-md fas fa-comment-medical fa-lg"></i>
                                        </button>
                                        <div class="popover"><?= htmlspecialchars($lineItem['notes']) ?></div>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                <?php endforeach; ?>
            </tbody>
        </table>
    <?php else: ?>
        <div class="alert alert-info" role="alert">
            <i class="fas fa-info-circle"></i>
            No draw request data available.
        </div>
    <?php endif; ?>

    <div class="d-flex justify-content-center btn-sm action-buttons">
        <button type="submit" name="btnSave" id="btnSave" class="btn btn-primary">Submit</button>
    </div>

</div>

<!-- Notes Modal -->
<div class="modal fade" id="noteModal" tabindex="-1" role="dialog" aria-labelledby="noteModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-sm" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="noteModalLabel">Edit Notes</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <textarea id="noteTextarea" class="form-control" rows="4" placeholder="Enter notes…"></textarea>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary btn-sm" data-dismiss="modal">Cancel</button>
                <button type="button" id="saveNoteBtn" class="btn btn-primary btn-sm">Save</button>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    let $currentNoteBtn;
    let $saveBtn = $('#btnSave');

    // Handle modal opening
    $('#noteModal').on('show.bs.modal', function (event) {
        $currentNoteBtn = $(event.relatedTarget);
        const noteText = $currentNoteBtn.data('note') || '';
        $('#noteTextarea').val(noteText);
    });

    // Handle save note button
    $('#saveNoteBtn').on('click', function () {
        const updatedNote = $('#noteTextarea').val();

        // Update button data
        $currentNoteBtn.data('note', updatedNote);
        $currentNoteBtn.attr('data-note', updatedNote);

        // Update the popover content
        const popover = $currentNoteBtn.siblings('.popover');
        popover.text(updatedNote);

        // Update icon color based on content
        const icon = $currentNoteBtn.find('i');
        if (updatedNote.trim()) {
            icon.removeClass('text-muted').addClass('text-primary');
        } else {
            icon.removeClass('text-primary').addClass('text-muted');
        }

        $('#noteModal').modal('hide');

        // If DrawManagement object exists, mark as modified
        if (typeof DrawManagement !== 'undefined') {
            DrawManagement.config.lineItemsModified = true;
            DrawManagement.elements.$saveLineItemsBtn.prop('disabled', false);
        }
    });

    // Initialize icon colors based on existing notes
    $('.note-btn').each(function() {
        const noteText = $(this).data('note') || '';
        const icon = $(this).find('i');
        if (noteText.trim()) {
            icon.addClass('text-primary');
        } else {
            icon.addClass('text-muted');
        }
    });

    // Reverse calculation and validation functions
    function calculateAmountFromPercent($percentInput) {
        const percent = parseFloat($percentInput.val()) || 0;
        const cost = parseFloat($percentInput.data('cost')) || 0;
        const requestedAmount = (percent / 100) * cost;

        return Math.max(0, requestedAmount);
    }

    function calculatePercentFromAmount($amountInput) {
        const amount = parseFloat($amountInput.val()) || 0;
        const cost = parseFloat($amountInput.data('cost')) || 0;

        const percent = (amount / cost) * 100;
        return Math.max(0, Math.min(100, percent));
    }

    function validatePercentInput($percentInput) {
        const percent = parseFloat($percentInput.val()) || 0;
        const completedPercent = parseFloat($percentInput.data('completed-percent')) || 0;
        const maxPercent = 100 - completedPercent;

        const $validationMsg = $percentInput.closest('td').find('.validation-message');

        if (percent < 0) {
            $validationMsg.text('Percentage cannot be negative').show();
            $saveBtn.prop('disabled', true);
            $percentInput.addClass('is-invalid');
            return false;
        } else if (percent > maxPercent) {
            $validationMsg.text(`Percentage cannot exceed ${maxPercent.toFixed(2)}% (100% - ${completedPercent.toFixed(2)}% completed)`).show();
            $percentInput.addClass('is-invalid');
            $saveBtn.prop('disabled', true);
            return false;
        } else {
            $validationMsg.hide();
            $percentInput.removeClass('is-invalid');
            $saveBtn.prop('disabled', false);
            return true;
        }
    }

    function validateAmountInput($amountInput) {
        const amount = parseFloat($amountInput.val()) || 0;
        const cost = parseFloat($amountInput.data('cost')) || 0;
        const completedAmount = parseFloat($amountInput.data('completed-amount')) || 0;
        const maxAmount = cost - completedAmount;

        const $validationMsg = $amountInput.closest('td').find('.validation-message');

        if (amount < 0) {
            $validationMsg.text('Amount cannot be negative').show();
            $amountInput.addClass('is-invalid');
            $saveBtn.prop('disabled', true);
            return false;
        } else if (amount > maxAmount) {
            $validationMsg.text(`Amount cannot exceed $${maxAmount.toFixed(2)} (Total Budget - Completed Renovations)`).show();
            $amountInput.addClass('is-invalid');
            $saveBtn.prop('disabled', true);
            return false;
        } else {
            $validationMsg.hide();
            $amountInput.removeClass('is-invalid');
            $saveBtn.prop('disabled', false);
            return true;
        }
    }

    // Event handlers for reverse calculation
    $('.requested-percent').on('input', function() {
        const $percentInput = $(this);
        const lineItemId = $percentInput.data('line-item-id');
        const $amountInput = $(`.requested-amount[data-line-item-id="${lineItemId}"]`);

        if (validatePercentInput($percentInput)) {
            const calculatedAmount = calculateAmountFromPercent($percentInput);
            $amountInput.val(calculatedAmount.toFixed(2));
            validateAmountInput($amountInput);
        }
    });

    $('.requested-amount').on('input', function() {
        const $amountInput = $(this);
        const lineItemId = $amountInput.data('line-item-id');
        const $percentInput = $(`.requested-percent[data-line-item-id="${lineItemId}"]`);

        if (validateAmountInput($amountInput)) {
            const calculatedPercent = calculatePercentFromAmount($amountInput);
            $percentInput.val(calculatedPercent.toFixed(2));
            validatePercentInput($percentInput);
        }
    });

    // Validate on blur to ensure final validation
    $('.requested-percent, .requested-amount').on('blur', function() {
        const $input = $(this);
        if ($input.hasClass('requested-percent')) {
            validatePercentInput($input);
        } else {
            validateAmountInput($input);
        }
    });
});
</script>
